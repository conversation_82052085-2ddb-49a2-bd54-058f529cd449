import { test, expect, type Page } from '@playwright/test';
import { ConsentPage } from '../pages/VirtualHospitalOld';
import locs from '../locs/consent/index';
import i18n, { Lang } from '../i18n';

let page: Page;
let patientPage: ConsentPage;
let adminPage: ConsentPage;
const lang = (process.env.LANG === 'th') ? 'th' : 'en';

// test.beforeEach(async ({ context }) => {
//   page = await context.newPage();
//   patientPage = new ConsentPage(page);
//   await page.goto(process.env.URL_USER!);
// });

// test.afterEach(async ({ context }) => {
//   await page.close();
// });

// test('TC_CNS_SVH_001 : Verify patient waiting to join the call.',{tag: '@oldVersion'}, async ({ context }) => {
//   await patientPage.isClickAccept();
//   await patientPage.page.waitForTimeout(5000);
//   await patientPage.isClickAccept();
//   await patientPage.isInputForm();
//   await patientPage.isClickSubmit();
//   await patientPage.isClickConfirm();

//   const adminPageBrowser = await context.newPage();
//   adminPage = new ConsentPage(adminPageBrowser);
//   await adminPage.page.goto(process.env.URL_ADMIN!);
//   await adminPage.isLoginBackOffice();
//   await adminPage.isStartCall();
//   await patientPage.page.waitForTimeout(3000);

//   await patientPage.page.bringToFront();
//   await patientPage.page.waitForTimeout(8000);

//   await adminPage.page.bringToFront();
//   await adminPage.page.waitForTimeout(3000);
//   // await page.bringToFront();
//   // await adminPage.page.waitForTimeout(30000);
//   await adminPage.isClickDashboard();
//   await adminPage.isClickNurseRoom();
//   await adminPage.isClickInfoButton('Liam CarterTest');
//   await adminPage.isEndCall();
// });

test('Run TC_CNS_SVH_001 10 times', { tag: '@oldVersion' }, async ({ context }) => {
  for (let i = 1; i <= 10; i++) {
    console.log(`Running iteration ${i}`);

    page = await context.newPage();
    patientPage = new ConsentPage(page);
    await page.goto(process.env.URL_USER!);

    await patientPage.isClickAccept();
    await patientPage.page.waitForTimeout(5000);
    await patientPage.isClickAccept();
    await patientPage.isInputForm();
    await patientPage.isClickSubmit();
    await patientPage.isClickConfirm();

    const adminPageBrowser = await context.newPage();
    adminPage = new ConsentPage(adminPageBrowser);
    await adminPage.page.goto(process.env.URL_ADMIN!);
    await adminPage.isLoginBackOffice();
    await adminPage.isStartCall();
    await patientPage.page.waitForTimeout(3000);

    await patientPage.page.bringToFront();
    await patientPage.page.waitForTimeout(8000);

    await adminPage.page.bringToFront();
    await adminPage.page.waitForTimeout(3000);

    await adminPage.isClickDashboard();
    await adminPage.isClickNurseRoom();
    await adminPage.isClickInfoButton('Liam CarterTest');
    await adminPage.isEndCall();

    await page.close();
    await adminPageBrowser.close();
  }
});

