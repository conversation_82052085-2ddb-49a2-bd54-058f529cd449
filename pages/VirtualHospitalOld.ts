import { Page, Locator } from '@playwright/test';
import locators from '../locs/virtualHospitalOld/index';
import i18n, { Lang } from '../i18n';

const lang = (process.env.LANG === 'th') ? 'th' : 'en';

export class ConsentPage {
  readonly page: Page;
  readonly i18n: typeof i18n;

  constructor(page: Page) {
      this.page = page;
      this.i18n = i18n;
  }

  async isClickAccept() {
    await this.page.waitForSelector(locators.btnAccept);
    await this.page.click(locators.btnAccept);
  }

  async isInputForm() {
    await this.page.waitForSelector(locators.inputFirstName);
    await this.page.click(locators.inputFirstName);
    await this.page.fill(locators.inputFirstName, "Liam");

    await this.page.waitForSelector(locators.inputLastName);
    await this.page.click(locators.inputLastName);
    await this.page.fill(locators.inputLastName, "CarterTest");

    await this.page.waitForSelector(locators.inputTel);
    await this.page.click(locators.inputTel);
    await this.page.fill(locators.inputTel, "0995033562");
  }

  async isClickSubmit() {
    await this.page.waitForSelector(locators.btnSubmit);
    await this.page.click(locators.btnSubmit);
  }

  async isRegister() {
    await this.isClickAccept();
    await this.isInputForm();
    await this.isClickSubmit();
  }

  async isClickConfirm() {
    await this.page.waitForSelector(locators.btnConfirm);
    await this.page.click(locators.btnConfirm);
  }

  async isConfirmService() {
    await this.isRegister();
    await this.isClickConfirm();
  }

  async isLoginBackOffice() {
    await this.page.waitForSelector(locators.inputUsername);
    await this.page.click(locators.inputUsername);
    await this.page.fill(locators.inputUsername, "adminuat");

    await this.page.waitForSelector(locators.inputPassword);
    await this.page.click(locators.inputPassword);
    await this.page.fill(locators.inputPassword, "temp1234*");

    await this.page.waitForSelector(locators.btnLogin);
    await this.page.click(locators.btnLogin);
  }

  async isStartCall() {
    await this.page.waitForSelector(locators.btnStartCall);
    await this.page.click(locators.btnStartCall);
  }

  async isLeaveCall() {
    await this.page.waitForSelector(locators.btnLeaveCall);
    await this.page.click(locators.btnLeaveCall);
  }

  async isClickDashboard() {
    await this.page.waitForSelector(locators.menuDashboard);
    await this.page.click(locators.menuDashboard);
  }

  async isClickNurseRoom() {
    await this.page.waitForSelector(locators.menuNurseRoom);
    await this.page.click(locators.menuNurseRoom);
  }

  async isClickWaitingNurse() {
    await this.page.waitForSelector(locators.menuWaitingNurse);
    await this.page.click(locators.menuWaitingNurse);
  }

  async isClickInfoButton(fullName: string) {
    //  ทั้งหมดเป็น div.col-md-3
    await this.page.waitForSelector("//div[@class='col-md-3']");
    const cards = await this.page.locator("//div[@class='col-md-3']").all();

    for (let i = 0; i < cards.length; i++) {
      const nameEl = await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//div[@class='name_customer']`).textContent();
      if (nameEl?.trim() === fullName) {
        await this.page.locator(`(//div[@class='col-md-3'])[${i + 1}]//a[normalize-space()='Information']`).click();
        return;
      }
    }
  }

  async isClickStartCallButton() {
    await this.page.waitForSelector(locators.startCallButton);
    await this.page.click(locators.startCallButton);
  }

  async isEndCallQueue() {
    await this.page.waitForSelector(locators.endCallQueueButton);
    await this.page.click(locators.endCallQueueButton);
  }

  async isConfirmEndCallQueue() {
    await this.page.waitForSelector(locators.endCallQueueConfirmButton);
    await this.page.click(locators.endCallQueueConfirmButton);
  }

  async isClickOkButton() {
    await this.page.waitForSelector(locators.confirmOkButton);
    await this.page.click(locators.confirmOkButton);
  }

  async isEndCall() {
    await this.page.waitForTimeout(3000);
    await this.isEndCallQueue();
    await this.page.waitForTimeout(3000);
    await this.isConfirmEndCallQueue();
    await this.page.waitForTimeout(3000);
    await this.isClickOkButton();
  }
}